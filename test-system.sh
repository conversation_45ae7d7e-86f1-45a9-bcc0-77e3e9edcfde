#!/bin/bash

# ONLYOFFICE Integration Test Script
# Kiểm tra toàn bộ hệ thống hoạt động

echo "🚀 ONLYOFFICE Integration Test Script"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

print_info() {
    echo -e "${YELLOW}ℹ️  $1${NC}"
}

# Check if Docker is running
print_info "Checking Docker..."
docker --version > /dev/null 2>&1
print_status $? "Docker is installed"

docker info > /dev/null 2>&1
print_status $? "Docker daemon is running"

# Check if docker compose is available
print_info "Checking Docker Compose..."
docker compose version > /dev/null 2>&1
if [ $? -eq 0 ]; then
    print_status 0 "Docker Compose v2 is available"
    DOCKER_COMPOSE="docker compose"
else
    docker-compose --version > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        print_status 0 "Docker Compose v1 is available"
        DOCKER_COMPOSE="docker-compose"
    else
        print_status 1 "Docker Compose not found"
        exit 1
    fi
fi

# Create necessary directories
print_info "Setting up directories..."
mkdir -p backend/files
mkdir -p backend/onlyoffice-data
print_status $? "Created necessary directories"

# Create a sample document if not exists
if [ ! -f "backend/files/test.docx" ]; then
    print_info "Creating sample document..."
    echo "Sample Document Content" > backend/files/test.txt
    print_status $? "Created sample text file (test.txt)"
fi

# Check if backend dependencies are installed
print_info "Checking backend dependencies..."
cd backend
if [ ! -d "node_modules" ]; then
    print_info "Installing Node.js dependencies..."
    npm install
    print_status $? "Installed Node.js dependencies"
else
    print_status 0 "Node.js dependencies already installed"
fi
cd ..

# Start the system
print_info "Starting ONLYOFFICE system..."
cd backend
$DOCKER_COMPOSE down > /dev/null 2>&1
$DOCKER_COMPOSE up -d

# Wait for services to start
print_info "Waiting for services to start..."
sleep 30

# Check if containers are running
print_info "Checking container status..."
BACKEND_STATUS=$($DOCKER_COMPOSE ps -q onlyoffice-backend | xargs docker inspect -f '{{.State.Status}}' 2>/dev/null)
ONLYOFFICE_STATUS=$($DOCKER_COMPOSE ps -q onlyoffice-document-server | xargs docker inspect -f '{{.State.Status}}' 2>/dev/null)

if [ "$BACKEND_STATUS" = "running" ]; then
    print_status 0 "Backend container is running"
else
    print_status 1 "Backend container is not running"
fi

if [ "$ONLYOFFICE_STATUS" = "running" ]; then
    print_status 0 "ONLYOFFICE container is running"
else
    print_status 1 "ONLYOFFICE container is not running"
fi

# Test backend API
print_info "Testing backend API..."
sleep 10
BACKEND_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3000/)
if [ "$BACKEND_RESPONSE" = "200" ]; then
    print_status 0 "Backend API is responding"
else
    print_status 1 "Backend API is not responding (HTTP: $BACKEND_RESPONSE)"
fi

# Test ONLYOFFICE API
print_info "Testing ONLYOFFICE API..."
ONLYOFFICE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/web-apps/apps/api/documents/api.js)
if [ "$ONLYOFFICE_RESPONSE" = "200" ]; then
    print_status 0 "ONLYOFFICE API is responding"
else
    print_status 1 "ONLYOFFICE API is not responding (HTTP: $ONLYOFFICE_RESPONSE)"
fi

# Test token generation
print_info "Testing token generation..."
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:3000/generate-token \
    -H "Content-Type: application/json" \
    -d '{"fileName": "test.txt", "fileType": "docx"}')

if echo "$TOKEN_RESPONSE" | grep -q "token"; then
    print_status 0 "Token generation is working"
    echo "Sample token response:"
    echo "$TOKEN_RESPONSE" | jq '.' 2>/dev/null || echo "$TOKEN_RESPONSE"
else
    print_status 1 "Token generation failed"
    echo "Response: $TOKEN_RESPONSE"
fi

cd ..

# Show logs if there are issues
print_info "Recent logs from containers:"
echo "--- Backend Logs ---"
$DOCKER_COMPOSE logs --tail=10 onlyoffice-backend

echo "--- ONLYOFFICE Logs ---"
$DOCKER_COMPOSE logs --tail=10 onlyoffice-document-server

echo ""
echo "======================================"
echo "🎯 Test Summary:"
echo "- Backend: http://localhost:3000"
echo "- ONLYOFFICE: http://localhost"
echo "- Test Frontend: Open test.html in browser"
echo ""
echo "📝 Next Steps:"
echo "1. Open test.html in your browser"
echo "2. Check browser console for any errors"
echo "3. Try editing a document"
echo "4. Check backend logs for callback messages"
echo ""
echo "🛠️  Useful Commands:"
echo "- View logs: $DOCKER_COMPOSE logs -f"
echo "- Stop system: $DOCKER_COMPOSE down"
echo "- Restart: $DOCKER_COMPOSE restart"
