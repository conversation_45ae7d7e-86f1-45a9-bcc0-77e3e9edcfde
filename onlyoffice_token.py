import jwt
import datetime

# <PERSON><PERSON> đổi các thông tin phù hợp
secret_key = "khaitn1"
payload = {
    "document": {
        "fileType": "docx",
        "key": "Khaitn3",
        "title": "test.docx",
        "url": "http://localhost:3000/files/test.docx",
        "permissions": {
            "edit": True
        }
    },
    "documentType": "word",
    "editorConfig": {
        "callbackUrl": "http://**********:3000/save-endpoint",
    }
}

token = jwt.encode(payload, secret_key, algorithm="HS256")
print(token)