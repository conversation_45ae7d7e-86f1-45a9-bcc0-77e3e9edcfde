#!/usr/bin/env python3
"""
ONLYOFFICE Token Generator Sample
Tạo JWT token để mở file trong ONLYOFFICE Document Server

Usage:
    python onlyoffice_token.py [filename] [filetype]

Examples:
    python onlyoffice_token.py test.txt txt
    python onlyoffice_token.py document.docx docx
"""

import jwt
import sys
import json
import time

def generate_token(filename="test.txt", filetype="txt", edit=True):
    """
    Tạo JWT token cho ONLYOFFICE

    Args:
        filename (str): Tên file
        filetype (str): Loại file (txt, docx, xlsx, pptx)
        edit (bool): <PERSON> phép chỉnh sửa hay không

    Returns:
        dict: Token và payload information
    """

    # Secret key phải giống với backend và ONLYOFFICE server
    secret_key = "khaitn3"

    # Xác đ<PERSON>nh document type
    document_type_map = {
        'txt': 'word',
        'docx': 'word',
        'doc': 'word',
        'xlsx': 'spreadsheet',
        'xls': 'spreadsheet',
        'pptx': 'presentation',
        'ppt': 'presentation',
        'pdf': 'word'
    }

    document_type = document_type_map.get(filetype.lower(), 'word')

    # Tạo payload
    payload = {
        "document": {
            "fileType": filetype,
            "key": f"key_{int(time.time())}_{filename}",
            "title": filename,
            "url": f"http://localhost:3000/files/{filename}",
            "permissions": {
                "edit": edit
            }
        },
        "documentType": document_type,
        "editorConfig": {
            "callbackUrl": "http://localhost:3000/save-endpoint"
        }
    }

    # Tạo JWT token
    token = jwt.encode(payload, secret_key, algorithm="HS256")

    return {
        "token": token,
        "payload": payload,
        "secret_key": secret_key
    }

def main():
    """Main function"""

    # Parse command line arguments
    filename = sys.argv[1] if len(sys.argv) > 1 else "test.txt"
    filetype = sys.argv[2] if len(sys.argv) > 2 else "txt"

    print(f"🔐 ONLYOFFICE Token Generator")
    print(f"============================")
    print(f"File: {filename}")
    print(f"Type: {filetype}")
    print()

    try:
        result = generate_token(filename, filetype)

        print("✅ Token generated successfully!")
        print()
        print("📋 Token:")
        print(result["token"])
        print()
        print("📋 Payload:")
        print(json.dumps(result["payload"], indent=2))
        print()
        print("🔧 Usage in JavaScript:")
        print("const config = {")
        print(f'  token: "{result["token"]}",')
        print("  ...payload")
        print("};")
        print()
        print("🌐 Test URL:")
        print(f"http://localhost:3000/files/{filename}")

    except Exception as e:
        print(f"❌ Error generating token: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()