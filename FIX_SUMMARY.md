# 🔧 Fix Summary: "Cannot GET /files/" Error

## ❌ Vấn đề ban đầu
Bạn gặp lỗi **"Cannot GET /files/"** khi cố gắng truy cập files từ ONLYOFFICE.

## 🔍 Nguyên nhân
1. **Volume mount sai**: `backend/docker-compose.yml` đang mount `/Users/<USER>/Documents` (macOS path) thay vì `./files`
2. **URL conflict**: Backend sử dụng container names (`onlyoffice-backend:3000`) nhưng browser không thể truy cập internal Docker network
3. **File không được mount**: Container `/app/files` trống vì volume mount không đúng

## ✅ Các fix đã áp dụng

### 1. Sửa Volume Mount
**File**: `backend/docker-compose.yml`
```yaml
# Trước
volumes:
  - /Users/<USER>/Documents:/app/files

# Sau  
volumes:
  - ./files:/app/files:rw
```

### 2. Thêm Hybrid URL Support
**File**: `backend/server.js`
- Thêm parameter `forBrowser` trong API `/generate-token`
- Khi `forBrowser=true`: sử dụng `localhost:3000` URLs
- Khi `forBrowser=false`: sử dụng `onlyoffice-backend:3000` URLs

```javascript
// URL file - sử dụng localhost cho browser, container name cho internal
const url = forBrowser 
    ? `http://localhost:3000/files/${fileName}`
    : `http://onlyoffice-backend:3000/files/${fileName}`;

const callbackUrl = forBrowser
    ? `http://localhost:3000/save-endpoint`
    : `http://onlyoffice-backend:3000/save-endpoint`;
```

### 3. Cập nhật Frontend
**File**: `test.html`
```javascript
// Thêm forBrowser: true cho browser requests
body: JSON.stringify({ 
    fileName, 
    fileType,
    forBrowser: true  // Sử dụng localhost URLs cho browser
})
```

### 4. Rebuild Container
- Rebuild container với `--build` flag để áp dụng code changes
- Verify volume mount hoạt động đúng

## 🧪 Kết quả test

### ✅ Files đã accessible:
```bash
$ curl http://localhost:3000/files/test.txt
ONLYOFFICE Integration Test Document
...
```

### ✅ Token generation với browser URLs:
```bash
$ curl -X POST http://localhost:3000/generate-token \
  -H "Content-Type: application/json" \
  -d '{"fileName": "test.txt", "fileType": "txt", "forBrowser": true}'

{
  "token": "eyJ...",
  "payload": {
    "document": {
      "url": "http://localhost:3000/files/test.txt"  // ✅ localhost URL
    },
    "editorConfig": {
      "callbackUrl": "http://localhost:3000/save-endpoint"  // ✅ localhost URL
    }
  }
}
```

### ✅ Container file mount:
```bash
$ docker exec onlyoffice-backend ls -la /app/files/
total 32
-rw-r--r-- 1 <USER> <GROUP> 166 sample.csv
-rw-r--r-- 1 <USER> <GROUP> 360 sample.html  
-rw-r--r-- 1 <USER> <GROUP> 500 sample.md
-rw-r--r-- 1 <USER> <GROUP>   1 test.docx
-rw-r--r-- 1 <USER> <GROUP> 283 test.txt     // ✅ Files có sẵn
```

## 🎯 Trạng thái hiện tại

### ✅ Hoạt động:
- ✅ Backend API responding (http://localhost:3000)
- ✅ ONLYOFFICE server responding (http://localhost)
- ✅ File serving từ `/files/*` endpoints
- ✅ Token generation với browser-compatible URLs
- ✅ Volume mount đúng từ `backend/files/` vào container
- ✅ Frontend có thể load và gọi API

### 🌐 URLs:
- **Backend**: http://localhost:3000
- **ONLYOFFICE**: http://localhost  
- **Files**: http://localhost:3000/files/test.txt
- **Frontend**: file:///home/<USER>/projects/poc/onlyoffice/test.html

## 🚀 Next Steps

1. **Test frontend**: Mở `test.html` trong browser và verify editor load
2. **Test editing**: Thử chỉnh sửa document và check callback logs
3. **Test other files**: Thử với `sample.csv`, `sample.html`, etc.

## 📝 Commands hữu ích

```bash
# Xem logs
docker logs onlyoffice-backend -f

# Test API
curl http://localhost:3000/files/test.txt

# Test token generation  
curl -X POST http://localhost:3000/generate-token \
  -H "Content-Type: application/json" \
  -d '{"fileName": "test.txt", "fileType": "txt", "forBrowser": true}'

# Restart nếu cần
cd backend && docker compose restart
```

---
**Status**: ✅ **FIXED** - Files đã accessible, frontend có thể load documents!
