<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>ONLYOFFICE Viewer</title>
    <script type="text/javascript" src="http://localhost/web-apps/apps/api/documents/api.js"></script>
    <style>
        /* Reset cơ bản và full-screen */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        html, body {
            width: 100%;
            height: 100%;
            overflow: hidden; /* Ẩn thanh cuộn */
            font-family: Arial, sans-serif;
        }
        #placeholder {
            width: 100vw;  /* Chiếm 100% chiều rộng màn hình */
            height: 100vh; /* Chiếm 100% chiều cao màn hình */
        }
    </style>
</head>
<body>
    <div id="placeholder"></div>

    <script type="text/javascript">
        async function getDocumentToken(fileName, fileType = 'txt') {
            const response = await fetch('http://localhost:3000/generate-token', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    fileName,
                    fileType,
                    forBrowser: true  // Sử dụng localhost URLs cho browser
                })
            });
            return await response.json();
        }

        // Function to show loading message
        function showLoading(message) {
            document.getElementById('placeholder').innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; width: 100vw; height: 100vh; font-size: 18px; text-align: center;">
                    <div>
                        <div style="margin-bottom: 20px;">📄</div>
                        <div>${message}</div>
                    </div>
                </div>
            `;
        }

        // Function to show error message
        function showError(message) {
            document.getElementById('placeholder').innerHTML = `
                <div style="display: flex; justify-content: center; align-items: center; width: 100vw; height: 100vh; color: red; font-size: 18px; text-align: center;">
                    <div>
                        <div style="margin-bottom: 20px;">❌</div>
                        <div>Không thể tải tài liệu:</div>
                        <div style="margin-top: 10px; font-size: 14px;">${message}</div>
                        <div style="margin-top: 20px; font-size: 14px; color: #666;">
                            <div>Kiểm tra:</div>
                            <div>• Backend server đang chạy (http://localhost:3000)</div>
                            <div>• ONLYOFFICE server đang chạy (http://localhost)</div>
                            <div>• File test.txt tồn tại trong backend/files/</div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Wrap your async code in an async IIFE
        (async () => {
            try {
                showLoading('Đang tải tài liệu...');

                // Test with a simple text file first
                const data = await getDocumentToken('test.txt', 'txt');
                console.log('Token received:', data.token);

                const config = {
                    document: {
                        fileType: data.payload.document.fileType,
                        key: data.payload.document.key,
                        title: data.payload.document.title,
                        url: data.payload.document.url,
                        permissions: data.payload.document.permissions
                    },
                    documentType: data.payload.documentType,
                    editorConfig: data.payload.editorConfig,
                    token: data.token
                };

                console.log('Config:', config);

                // Khởi tạo ONLYOFFICE
                const docEditor = new DocsAPI.DocEditor("placeholder", config);

                // (Tùy chọn) Xử lý resize nếu cần
                window.addEventListener('resize', () => {
                    console.log('Window resized');
                    // ONLYOFFICE tự động xử lý resize
                });

            } catch (error) {
                console.error('Failed to load document:', error);
                showError(error.message);
            }
        })();
    </script>
</body>
</html>