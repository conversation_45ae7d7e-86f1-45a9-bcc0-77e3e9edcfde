# 🚀 ONLYOFFICE Integration - Quick Start Guide

## Khởi động nhanh (5 phút)

### 1. Setup ban đầu
```bash
# Clone và setup
git clone <repository-url>
cd onlyoffice

# Setup tự động
make setup
```

### 2. Khởi động hệ thống
```bash
# Khởi động toàn bộ hệ thống
make start

# Hoặc chỉ development environment
make dev
```

### 3. <PERSON><PERSON><PERSON> tra hệ thống
```bash
# Chạy test tự động
make test

# Hoặc kiểm tra thủ công
make test-api
```

### 4. Truy cập ứng dụng
- **ONLYOFFICE Server**: http://localhost
- **Backend API**: http://localhost:3000  
- **Test Frontend**: http://localhost:8080 (nếu dùng `make start`)
- **Test Frontend**: Mở file `test.html` (nếu dùng `make dev`)

## 🧪 Test nhanh

### Test với trình duyệt
1. Mở `test.html` trong trình duyệt
2. <PERSON><PERSON><PERSON> tra console để xem token
3. Verify editor load thành công
4. Thử chỉnh sửa document

### Test với API
```bash
# Test token generation
curl -X POST http://localhost:3000/generate-token \
  -H "Content-Type: application/json" \
  -d '{"fileName": "test.txt", "fileType": "txt"}'

# Test với Python script
python onlyoffice_token.py test.txt txt
```

## 🔧 Commands hữu ích

```bash
# Xem logs
make logs

# Restart hệ thống
make restart

# Dừng hệ thống
make stop

# Cleanup hoàn toàn
make clean

# Xem status
make status
```

## 📁 File structure sau setup

```
onlyoffice/
├── backend/
│   ├── files/           # Sample files
│   │   ├── test.txt
│   │   ├── sample.md
│   │   ├── sample.html
│   │   └── sample.csv
│   ├── server.js        # Backend server
│   ├── Dockerfile       # Backend container
│   └── docker-compose.yml
├── test.html           # Frontend test
├── onlyoffice_token.py # Python token generator
├── docker-compose.yml # Complete system
├── Makefile           # Management commands
└── README.md          # Full documentation
```

## ❗ Troubleshooting nhanh

### Lỗi thường gặp:

1. **Port đã được sử dụng**:
   ```bash
   make stop
   make clean
   make start
   ```

2. **CORS Error**:
   - Kiểm tra backend đang chạy: http://localhost:3000
   - Mở test.html từ file:// hoặc http://

3. **JWT Error**:
   - Verify secret key giống nhau trong tất cả services
   - Check logs: `make logs`

4. **File not found**:
   - Chạy lại: `./create-sample-files.sh`
   - Kiểm tra: `ls backend/files/`

### Debug commands:
```bash
# Kiểm tra containers
docker ps

# Xem logs chi tiết
docker-compose -f docker-compose.yml logs -f

# Test connectivity
docker exec onlyoffice-backend ping onlyoffice-document-server
```

## 🎯 Next Steps

1. **Customize**: Chỉnh sửa `backend/server.js` cho logic riêng
2. **Add Files**: Thêm file test vào `backend/files/`
3. **Frontend**: Customize `test.html` cho UI riêng
4. **Integration**: Tích hợp vào ứng dụng hiện có

## 📞 Support

- Xem full documentation: `README.md`
- Check logs: `make logs`
- Run tests: `make test`
