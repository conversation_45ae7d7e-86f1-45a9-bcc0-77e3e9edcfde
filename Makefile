# ONLYOFFICE Integration Makefile
# Provides easy commands to manage the ONLYOFFICE integration system

.PHONY: help setup start stop restart logs test clean install-deps create-files

# Default target
help:
	@echo "🚀 ONLYOFFICE Integration Management"
	@echo "=================================="
	@echo ""
	@echo "Available commands:"
	@echo "  setup        - Initial setup (install deps, create files)"
	@echo "  start        - Start the complete system"
	@echo "  stop         - Stop all services"
	@echo "  restart      - Restart all services"
	@echo "  logs         - Show logs from all services"
	@echo "  test         - Run system tests"
	@echo "  clean        - Clean up containers and volumes"
	@echo "  install-deps - Install Node.js dependencies"
	@echo "  create-files - Create sample test files"
	@echo ""
	@echo "🌐 URLs after starting:"
	@echo "  - ONLYOFFICE: http://localhost"
	@echo "  - Backend API: http://localhost:3000"
	@echo "  - Test Frontend: http://localhost:8080"

# Initial setup
setup: install-deps create-files
	@echo "✅ Setup completed!"
	@echo "Run 'make start' to start the system"

# Install Node.js dependencies
install-deps:
	@echo "📦 Installing Node.js dependencies..."
	cd backend && npm install

# Create sample files
create-files:
	@echo "📁 Creating sample files..."
	chmod +x create-sample-files.sh
	./create-sample-files.sh

# Start the complete system
start:
	@echo "🚀 Starting ONLYOFFICE integration system..."
	docker compose -f docker-compose.yml up -d
	@echo ""
	@echo "⏳ Waiting for services to start..."
	@sleep 30
	@echo ""
	@echo "✅ System started! Check status with 'make logs'"
	@echo ""
	@echo "🌐 Available URLs:"
	@echo "  - ONLYOFFICE: http://localhost"
	@echo "  - Backend API: http://localhost:3000"
	@echo "  - Test Frontend: http://localhost:8080"

# Stop all services
stop:
	@echo "🛑 Stopping ONLYOFFICE integration system..."
	docker compose -f docker-compose.yml down

# Restart all services
restart: stop start

# Show logs from all services
logs:
	@echo "📋 Showing logs from all services..."
	docker compose -f docker-compose.yml logs --tail=50 -f

# Run system tests
test:
	@echo "🧪 Running system tests..."
	chmod +x test-system.sh
	./test-system.sh

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers, images, and volumes..."
	-docker compose -f docker-compose.yml down -v --remove-orphans 2>/dev/null || true
	-docker compose -f backend/docker-compose.yml down -v --remove-orphans 2>/dev/null || true
	@echo "✅ Cleanup completed!"

# Quick start for development
dev:
	@echo "🔧 Starting development environment..."
	cd backend && docker compose up -d
	@echo "✅ Development environment started!"
	@echo "  - ONLYOFFICE: http://localhost"
	@echo "  - Backend: http://localhost:3000"
	@echo "  - Open test.html in your browser"

# Show system status
status:
	@echo "📊 System Status:"
	@echo "=================="
	@docker compose -f docker-compose.yml ps 2>/dev/null || echo "Full system not running"
	@echo ""
	@echo "Development environment:"
	@cd backend && docker compose ps 2>/dev/null || echo "Dev environment not running"

# Test API endpoints
test-api:
	@echo "🔍 Testing API endpoints..."
	@echo "Testing backend health..."
	@curl -s http://localhost:3000/ > /dev/null && echo "✅ Backend is responding" || echo "❌ Backend not responding"
	@echo "Testing ONLYOFFICE API..."
	@curl -s http://localhost/web-apps/apps/api/documents/api.js > /dev/null && echo "✅ ONLYOFFICE is responding" || echo "❌ ONLYOFFICE not responding"
	@echo "Testing token generation..."
	@curl -s -X POST http://localhost:3000/generate-token \
		-H "Content-Type: application/json" \
		-d '{"fileName": "test.txt", "fileType": "txt"}' | \
		grep -q "token" && echo "✅ Token generation working" || echo "❌ Token generation failed"
