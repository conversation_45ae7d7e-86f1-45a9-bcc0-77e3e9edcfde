# Complete ONLYOFFICE Integration System
# This docker-compose file sets up the entire system for testing

networks:
  onlyoffice-network:
    driver: bridge

volumes:
  onlyoffice-data:
    driver: local

services:
  # ONLYOFFICE Document Server
  onlyoffice-document-server:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-document-server
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=khaitn3
      - JWT_HEADER=Authorization
      - JWT_IN_BODY=true
    volumes:
      - onlyoffice-data:/var/www/onlyoffice/Data
      - ./mount:/var/www/onlyoffice/documentserver/mount:ro
    networks:
      - onlyoffice-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/web-apps/apps/api/documents/api.js"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend Node.js Server
  onlyoffice-backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: onlyoffice-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - JWT_SECRET=khaitn3
    volumes:
      - ./backend/files:/app/files:rw
      - ./backend:/app:ro
    networks:
      - onlyoffice-network
    depends_on:
      - onlyoffice-document-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx for serving static files (optional)
  nginx:
    image: nginx:alpine
    container_name: onlyoffice-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./test.html:/usr/share/nginx/html/index.html:ro
      - ./backend/files:/usr/share/nginx/html/files:ro
    networks:
      - onlyoffice-network
    depends_on:
      - onlyoffice-backend
