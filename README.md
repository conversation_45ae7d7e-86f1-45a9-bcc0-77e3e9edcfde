### Stand alone POC sample
```
docker run -d -p 80:80 -p 443:443 --name onlyoffice --network general -e JWT_SECRET=khaitn3 onlyoffice/documentserver
docker exec -it onlyoffice bash
nano /etc/onlyoffice/documentserver/local.json

docker restart onlyoffice

docker exec onlyoffice /var/www/onlyoffice/documentserver/npm/json -f /etc/onlyoffice/documentserver/local.json
```

### Config with nextcloud
Run docker-compose

```
openssl rand -base64 32
client secret: <set in nextcloud>
```