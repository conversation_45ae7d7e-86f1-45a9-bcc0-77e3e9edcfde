# ONLYOFFICE Integration POC

Dự án này là một Proof of Concept (POC) để tích hợp ONLYOFFICE Document Server với backend Node.js, bao gồm việc tạo JWT token và xử lý callback khi file được chỉnh sửa.

## 🏗️ Kiến trúc hệ thống

Hệ thống bao gồm 5 thành phần chính:

### 1. Backend Node.js (`backend/server.js`)
- **Mục đích**: Server backend để xử lý API và callback từ ONLYOFFICE
- **Chức năng**:
  - Tạo JWT token cho việc mở file trong ONLYOFFICE
  - Nhận callback từ ONLYOFFICE khi file được chỉnh sửa
  - Phục vụ file tĩnh từ thư mục `/files`
- **Port**: 3000
- **API Endpoints**:
  - `POST /generate-token`: Tạo JWT token
  - `POST /save-endpoint`: Nhận callback từ ONLYOFFICE
  - `GET /files/*`: <PERSON><PERSON><PERSON> vụ file tĩnh

### 2. Token Generator Python (`onlyoffice_token.py`)
- **Mục đích**: Sample script Python để tạo JWT token
- **Chức năng**: Tạo token JWT với cấu hình cơ bản cho ONLYOFFICE
- **Sử dụng**: Để test hoặc tham khảo cách tạo token

### 3. Frontend Test (`test.html`)
- **Mục đích**: Giao diện web để test ONLYOFFICE editor
- **Chức năng**:
  - Gọi API backend để lấy token
  - Khởi tạo ONLYOFFICE editor với token
  - Hiển thị document trong trình duyệt
- **Cấu hình**: Full-screen editor

### 4. Docker Configuration
#### Backend Dockerfile (`backend/Dockerfile`)
- **Base image**: node:18-alpine
- **Port**: 3000
- **Chức năng**: Build image cho backend Node.js

#### Docker Compose (`backend/docker-compose.yml`)
- **Services**:
  - `onlyoffice-document-server`: ONLYOFFICE server (port 80)
  - `onlyoffice-backend`: Backend Node.js (port 3000)
- **Network**: onlyoffice-network
- **JWT**: Enabled với secret key

### 5. Alternative Docker Compose (`dockercompose/docker-compose.yml`)
- **Services**: NextCloud + ONLYOFFICE
- **Mục đích**: Setup alternative với NextCloud integration

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Docker & Docker Compose
- Node.js 18+ (nếu chạy local)
- Python 3.x (cho token generator)

### Chạy với Docker Compose (Khuyến nghị)

1. **Clone repository**:
```bash
git clone <repository-url>
cd onlyoffice
```

2. **Tạo thư mục files và copy file test**:
```bash
mkdir -p backend/files
# Copy file test vào backend/files/
```

3. **Chạy hệ thống**:
```bash
cd backend
docker-compose up -d
```

4. **Kiểm tra services**:
```bash
docker-compose ps
```

### Chạy local (Development)

1. **Chạy ONLYOFFICE server**:
```bash
docker run -d -p 80:80 \
  -e JWT_ENABLED=true \
  -e JWT_SECRET=khaitn3 \
  onlyoffice/documentserver:latest
```

2. **Chạy backend**:
```bash
cd backend
npm install
npm start
```

3. **Mở test.html trong trình duyệt**

## 🧪 Testing

### Test hoàn chỉnh hệ thống

1. **Kiểm tra ONLYOFFICE server**:
   - Truy cập: http://localhost
   - Kiểm tra API: http://localhost/web-apps/apps/api/documents/api.js

2. **Kiểm tra backend**:
   - Truy cập: http://localhost:3000
   - Test API:
   ```bash
   curl -X POST http://localhost:3000/generate-token \
     -H "Content-Type: application/json" \
     -d '{"fileName": "test.docx", "fileType": "docx"}'
   ```

3. **Test frontend**:
   - Mở `test.html` trong trình duyệt
   - Kiểm tra console để xem token và config
   - Verify editor load thành công

4. **Test callback**:
   - Mở file trong editor
   - Chỉnh sửa và save
   - Kiểm tra log backend để xem callback

### Test với Python token generator

```bash
pip install PyJWT
python onlyoffice_token.py
```

## 🔧 Cấu hình

### JWT Secret Key
- Backend: `khaitn3` (trong server.js)
- ONLYOFFICE: `khaitn3` (trong docker-compose.yml)
- Python: `khaitn1` (trong onlyoffice_token.py)

**Lưu ý**: Đảm bảo secret key giống nhau giữa backend và ONLYOFFICE

### File Paths
- Backend files: `backend/files/`
- Docker volume: Mount thư mục local vào `/app/files`

### Network Configuration
- ONLYOFFICE: port 80
- Backend: port 3000
- Network: onlyoffice-network (bridge)

## 📝 API Documentation

### POST /generate-token
**Request**:
```json
{
  "fileName": "test.docx",
  "fileType": "docx",
  "edit": true
}
```

**Response**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiJ9...",
  "payload": {
    "document": {
      "fileType": "docx",
      "key": "key_1234567890_test.docx",
      "title": "test.docx",
      "url": "http://onlyoffice-backend:3000/files/test.docx",
      "permissions": {
        "edit": true
      }
    },
    "documentType": "word",
    "editorConfig": {
      "callbackUrl": "http://onlyoffice-backend:3000/save-endpoint"
    }
  },
  "documentUrl": "http://onlyoffice-backend:3000/files/test.docx",
  "message": "Token generated successfully"
}
```

### POST /save-endpoint
**Callback từ ONLYOFFICE khi file được chỉnh sửa**

**Request từ ONLYOFFICE**:
```json
{
  "status": 2,
  "key": "key_1234567890_test.docx",
  "url": "https://onlyoffice-server/cache/files/..."
}
```

**Status codes**:
- `1`: Document opened
- `2`: Document saved
- `6`: Session ended

## 🐛 Troubleshooting

### Common Issues

1. **CORS Error**:
   - Kiểm tra CORS headers trong backend
   - Verify domain và port

2. **JWT Error**:
   - Kiểm tra secret key giống nhau
   - Verify JWT format

3. **File not found**:
   - Kiểm tra file path trong backend/files/
   - Verify volume mount trong Docker

4. **Callback không hoạt động**:
   - Kiểm tra network connectivity
   - Verify callback URL accessible từ ONLYOFFICE container

### Debug Commands

```bash
# Kiểm tra logs
docker-compose logs onlyoffice-backend
docker-compose logs onlyoffice-document-server

# Kiểm tra network
docker network ls
docker network inspect backend_onlyoffice-network

# Test connectivity
docker exec onlyoffice-backend ping onlyoffice-document-server
```

## 📚 Tài liệu tham khảo

- [ONLYOFFICE API Documentation](https://api.onlyoffice.com/)
- [ONLYOFFICE Docker](https://github.com/ONLYOFFICE/Docker-DocumentServer)
- [JWT.io](https://jwt.io/) - JWT Debugger

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch
3. Commit changes
4. Push và tạo Pull Request

## 📄 License

MIT License

---

### Stand alone POC sample (Legacy)
```bash
docker run -d -p 80:80 -p 443:443 --name onlyoffice --network general -e JWT_SECRET=khaitn3 onlyoffice/documentserver
docker exec -it onlyoffice bash
nano /etc/onlyoffice/documentserver/local.json

docker restart onlyoffice

docker exec onlyoffice /var/www/onlyoffice/documentserver/npm/json -f /etc/onlyoffice/documentserver/local.json
```

### Config with nextcloud (Legacy)
```bash
openssl rand -base64 32
# client secret: <set in nextcloud>
```