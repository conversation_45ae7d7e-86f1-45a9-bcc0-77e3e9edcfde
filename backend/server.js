const express = require('express');
const path = require('path');
const bodyParser = require('body-parser');
const jwt = require('jsonwebtoken');

const app = express();
const PORT = 3000;

// C<PERSON>u hình body parser
app.use(bodyParser.json());

// Middleware CORS
app.use((req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET, POST, PUT, OPTIONS");
    res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
    console.log('Request:', req.method, req.url);
    next();
});

// ======================
// Web Server: Phục vụ file công cộng
// ======================
const DOCUMENTS_DIR = path.join(__dirname, 'files');
app.use('/files', express.static(DOCUMENTS_DIR));

app.get('/', (req, res) => {
    res.send(`
        <h1>ONLYOFFICE Backend Server</h1>
        <p>Files are served at <a href="/files">/files</a></p>
        <p>API: POST /generate-token</p>
    `);
});

// ======================
// API: Sinh JWT Token
// ======================
const SECRET_KEY = "khaitn3";

app.post("/generate-token", async (req, res) => {
    try {
        const { fileName, fileType = 'docx', edit = true } = req.body;

        if (!fileName) {
            return res.status(400).json({ error: 'fileName is required' });
        }

        // Xác định documentType và fileType dựa trên extension
        let documentType = 'word';
        let actualFileType = fileType;

        if (fileType === 'pptx') {
            documentType = 'presentation';
        } else if (fileType === 'xlsx') {
            documentType = 'spreadsheet';
        } else if (fileType === 'pdf') {
            documentType = 'word';
        } else if (fileType === 'txt') {
            documentType = 'word';
            actualFileType = 'txt';
        }

        // URL file trên MinIO
        const url = `http://localhost:3000/files/${fileName}`;
        // const url = `https://kailab.fun/minio/learning/docs/test.docx`;

        // Tạo payload với thông tin file thực tế
        const payload = {
            document: {
                fileType: actualFileType,
                key: `key_${Date.now()}_${fileName}`,
                title: fileName,
                url: url,
                permissions: {
                    edit: Boolean(edit)
                }
            },
            documentType: documentType,
            editorConfig: {
                callbackUrl: `http://localhost:3000/save-endpoint`
            }
        };

        console.log('Generated payload:', JSON.stringify(payload, null, 2));

        // Tạo JWT token
        const token = jwt.sign(payload, SECRET_KEY, { 
          algorithm: 'HS256',
          noTimestamp: true
        });
        console.log(token);

        // Trả về token và thông tin
        res.json({
            token,
            payload,
            documentUrl: url,
            message: "Token generated successfully"
        });

    } catch (error) {
        console.error('Error generating token:', error);
        res.status(500).json({ error: 'Failed to generate token' });
    }
});

// ======================
// API: Callback từ ONLYOFFICE
// ======================
app.post("/save-endpoint", (req, res) => {
    const { status, key, url } = req.body;

    console.log('Callback received:', req.body);

    // Luôn trả về 200 OK cho ONLYOFFICE
    res.status(200).json({ "error": 0, "message": "Callback received" });

    // Xử lý các trạng thái
    switch (status) {
        case 1:
            console.log(`Tài liệu "${key}" đã được mở`);
            break;
        case 2:
            console.log(`Tài liệu "${key}" đã được lưu! URL: ${url}`);
            break;
        case 6:
            console.log(`Phiên làm việc với "${key}" đã kết thúc. URL = ${url}`);
            break;
        default:
            console.log(`Trạng thái không xác định: ${status}`);
    }
});

// ======================
// Khởi động server
// ======================
// === Khởi động server ===
app.listen(PORT, '0.0.0.0', () => {
    console.log(`✅ Backend Server is running on http://localhost:${PORT}`);
    console.log(`📁 Serving files from: ${DOCUMENTS_DIR}`);
    console.log(`🔗 API: POST /generate-token`);
    console.log(`🔗 Callback: POST /save-endpoint`);
});