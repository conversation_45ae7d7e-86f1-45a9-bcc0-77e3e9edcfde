# docker-compose.yml
version: '3.8'

services:
  onlyoffice-document-server:
    image: onlyoffice/documentserver:latest
    container_name: onlyoffice-document-server
    ports:
      - "80:80"
    environment:
      - JWT_ENABLED=true
      - JWT_SECRET=khaitn3
      - JWT_HEADER=Authorization
    volumes:
      - ./onlyoffice-data:/var/www/onlyoffice/Data
    networks:
      - onlyoffice-network

  onlyoffice-backend:
    build: ./
    container_name: onlyoffice-backend
    ports:
      - "3000:3000"
    volumes:
      - /Users/<USER>/Documents:/app/files
    networks:
      - onlyoffice-network

networks:
  onlyoffice-network:
    driver: bridge